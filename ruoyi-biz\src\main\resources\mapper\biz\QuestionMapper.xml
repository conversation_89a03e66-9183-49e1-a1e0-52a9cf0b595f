<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.biz.mapper.QuestionMapper">
    
    <resultMap type="Question" id="QuestionResult">
        <result property="questionId"    column="question_id"    />
        <result property="bankId"    column="bank_id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="pointIds"    column="point_ids"    />
        <result property="questionType"    column="question_type"    />
        <result property="difficulty"    column="difficulty"    />
        <result property="questionContent"    column="question_content"    />
        <result property="analysis"    column="analysis"    />
        <result property="options"    column="options"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectQuestionVo">
        select question_id, bank_id, category_id, point_ids, question_type, difficulty, question_content, analysis, options, create_by, create_time, update_time from tbl_question
    </sql>

    <select id="selectQuestionList" parameterType="Question" resultMap="QuestionResult">
        <include refid="selectQuestionVo"/>
        <where>  
            <if test="bankId != null "> and bank_id = #{bankId}</if>
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="pointIds != null  and pointIds != ''"> and point_ids = #{pointIds}</if>
            <if test="questionType != null "> and question_type = #{questionType}</if>
            <if test="difficulty != null "> and difficulty = #{difficulty}</if>
            <if test="questionContent != null  and questionContent != ''"> and question_content like concat('%', #{questionContent}, '%')</if>
            <if test="analysis != null  and analysis != ''"> and analysis = #{analysis}</if>
            <if test="options != null  and options != ''"> and options = #{options}</if>
        </where>
        order by question_id desc
    </select>
    
    <select id="selectQuestionByQuestionId" parameterType="Long" resultMap="QuestionResult">
        <include refid="selectQuestionVo"/>
        where question_id = #{questionId}
    </select>

    <insert id="insertQuestion" parameterType="Question" useGeneratedKeys="true" keyProperty="questionId">
        insert into tbl_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bankId != null">bank_id,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="pointIds != null">point_ids,</if>
            <if test="questionType != null">question_type,</if>
            <if test="difficulty != null">difficulty,</if>
            <if test="questionContent != null and questionContent != ''">question_content,</if>
            <if test="analysis != null">analysis,</if>
            <if test="options != null">options,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bankId != null">#{bankId},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="pointIds != null">#{pointIds},</if>
            <if test="questionType != null">#{questionType},</if>
            <if test="difficulty != null">#{difficulty},</if>
            <if test="questionContent != null and questionContent != ''">#{questionContent},</if>
            <if test="analysis != null">#{analysis},</if>
            <if test="options != null">#{options},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateQuestion" parameterType="Question">
        update tbl_question
        <trim prefix="SET" suffixOverrides=",">
            <if test="bankId != null">bank_id = #{bankId},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="pointIds != null">point_ids = #{pointIds},</if>
            <if test="questionType != null">question_type = #{questionType},</if>
            <if test="difficulty != null">difficulty = #{difficulty},</if>
            <if test="questionContent != null and questionContent != ''">question_content = #{questionContent},</if>
            <if test="analysis != null">analysis = #{analysis},</if>
            <if test="options != null">options = #{options},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where question_id = #{questionId}
    </update>

    <delete id="deleteQuestionByQuestionId" parameterType="Long">
        delete from tbl_question where question_id = #{questionId}
    </delete>

    <delete id="deleteQuestionByQuestionIds" parameterType="String">
        delete from tbl_question where question_id in
        <foreach item="questionId" collection="array" open="(" separator="," close=")">
            #{questionId}
        </foreach>
    </delete>

    <select id="selectQuestionStatistics" parameterType="Long" resultType="java.util.Map">
        SELECT
            CAST(question_type AS SIGNED) as question_type,
            CAST(COUNT(*) AS SIGNED) as count
        FROM tbl_question
        WHERE bank_id = #{bankId}
        AND question_type IN (1, 2, 3)
        GROUP BY question_type
    </select>

    <select id="selectQuestionsByIds" parameterType="java.util.List" resultMap="QuestionResult">
        <include refid="selectQuestionVo"/>
        where question_id in
        <foreach item="questionId" collection="list" open="(" separator="," close=")">
            #{questionId}
        </foreach>
        order by question_id desc
    </select>
</mapper>