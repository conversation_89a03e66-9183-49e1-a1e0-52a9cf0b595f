package com.ruoyi.biz.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Arrays;
import java.util.ArrayList;
import javax.servlet.http.HttpServletResponse;
import org.apache.poi.xwpf.usermodel.*;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.biz.mapper.QuestionMapper;
import com.ruoyi.biz.domain.Question;
import com.ruoyi.biz.service.IQuestionService;
import com.ruoyi.biz.converter.QuestionConverter;
import com.ruoyi.biz.dto.QuestionDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * 题目Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Slf4j
@Service
public class QuestionServiceImpl implements IQuestionService
{
    @Autowired
    private QuestionMapper questionMapper;

    @Autowired
    private QuestionConverter questionConverter;

    /**
     * 查询题目
     * 
     * @param questionId 题目主键
     * @return 题目
     */
    @Override
    public Question selectQuestionByQuestionId(Long questionId)
    {
        return questionMapper.selectQuestionByQuestionId(questionId);
    }

    /**
     * 查询题目列表
     * 
     * @param question 题目
     * @return 题目
     */
    @Override
    public List<Question> selectQuestionList(Question question)
    {
        return questionMapper.selectQuestionList(question);
    }

    /**
     * 新增题目
     * 
     * @param question 题目
     * @return 结果
     */
    @Override
    public int insertQuestion(Question question)
    {
        question.setCreateTime(DateUtils.getNowDate());
        // 如果没有设置创建人，则设置当前用户
        if (question.getCreateBy() == null || question.getCreateBy().isEmpty()) {
            try {
                question.setCreateBy(SecurityUtils.getUsername());
            } catch (Exception e) {
                question.setCreateBy("admin");
            }
        }
        return questionMapper.insertQuestion(question);
    }

    /**
     * 修改题目
     *
     * @param question 题目
     * @return 结果
     */
    @Override
    public int updateQuestion(Question question)
    {
        question.setUpdateTime(DateUtils.getNowDate());
        return questionMapper.updateQuestion(question);
    }

    /**
     * 批量插入题目
     *
     * @param questions 题目列表
     * @return 插入的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsertQuestions(List<Question> questions)
    {
        if (questions == null || questions.isEmpty()) {
            return 0;
        }

        // 设置公共字段
        String currentUser;
        try {
            currentUser = SecurityUtils.getUsername();
        } catch (Exception e) {
            currentUser = "admin";
        }

        java.util.Date now = DateUtils.getNowDate();
        for (Question question : questions) {
            question.setCreateTime(now);
            if (question.getCreateBy() == null || question.getCreateBy().isEmpty()) {
                question.setCreateBy(currentUser);
            }
        }

        // 分批插入，避免SQL过长
        int totalInserted = 0;
        int batchSize = 50; // 每批50条记录

        for (int i = 0; i < questions.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, questions.size());
            List<Question> batch = questions.subList(i, endIndex);

            try {
                int inserted = questionMapper.batchInsertQuestions(batch);
                totalInserted += inserted;
                log.info("批量插入进度: {}/{}, 本批插入: {}", endIndex, questions.size(), inserted);
            } catch (Exception e) {
                log.error("批量插入失败，批次: {}-{}", i, endIndex, e);
                throw new RuntimeException("批量插入失败: " + e.getMessage(), e);
            }
        }

        return totalInserted;
    }

    /**
     * 批量删除题目（优化版本）
     *
     * @param questionIds 需要删除的题目主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteQuestionByQuestionIds(Long[] questionIds)
    {
        if (questionIds == null || questionIds.length == 0) {
            return 0;
        }

        int totalDeleted = 0;
        int batchSize = 20; // 每批处理20条，避免超时

        // 分批删除
        for (int i = 0; i < questionIds.length; i += batchSize) {
            int endIndex = Math.min(i + batchSize, questionIds.length);
            Long[] batchIds = Arrays.copyOfRange(questionIds, i, endIndex);

            try {
                int deleted = questionMapper.deleteQuestionByQuestionIds(batchIds);
                totalDeleted += deleted;

                // 记录删除进度
                System.out.println(String.format("批量删除进度: %d/%d, 本批删除: %d",
                    endIndex, questionIds.length, deleted));

                // 短暂休息，避免数据库压力过大
                if (i + batchSize < questionIds.length) {
                    Thread.sleep(50); // 休息50毫秒
                }
            } catch (Exception e) {
                System.err.println("删除题目批次失败: " + Arrays.toString(batchIds) + ", 错误: " + e.getMessage());
                throw new RuntimeException("批量删除失败: " + e.getMessage(), e);
            }
        }

        return totalDeleted;
    }

    /**
     * 删除题目信息
     *
     * @param questionId 题目主键
     * @return 结果
     */
    @Override
    public int deleteQuestionByQuestionId(Long questionId)
    {
        return questionMapper.deleteQuestionByQuestionId(questionId);
    }

    /**
     * 获取题库统计信息
     *
     * @param bankId 题库ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getQuestionStatistics(Long bankId)
    {
        List<Map<String, Object>> statistics = questionMapper.selectQuestionStatistics(bankId);

        // 初始化统计数据
        Map<String, Object> result = new HashMap<>();
        result.put("total", 0);
        result.put("singleChoice", 0);
        result.put("multipleChoice", 0);
        result.put("judgment", 0);

        int total = 0;

        // 处理统计结果
        for (Map<String, Object> stat : statistics) {
            try {
                // 获取统计数据
                Object questionTypeObj = stat.get("question_type");
                Object countObj = stat.get("count");

                if (questionTypeObj == null || countObj == null) {
                    continue;
                }

                // 简单的数字转换
                int questionType = 0;
                int count = 0;

                // 处理question_type
                if (questionTypeObj instanceof Number) {
                    questionType = ((Number) questionTypeObj).intValue();
                } else {
                    String str = questionTypeObj.toString().trim();
                    if (str.matches("^\\d+$")) {
                        questionType = Integer.parseInt(str);
                    } else {
                        continue; // 跳过无效数据
                    }
                }

                // 处理count
                if (countObj instanceof Number) {
                    count = ((Number) countObj).intValue();
                } else {
                    String str = countObj.toString().trim();
                    if (str.matches("^\\d+$")) {
                        count = Integer.parseInt(str);
                    } else {
                        continue; // 跳过无效数据
                    }
                }

                // 只处理有效的题型和数量
                if (count > 0 && questionType >= 1 && questionType <= 3) {
                    total += count;

                    switch (questionType) {
                        case 1: // 单选题
                            result.put("singleChoice", count);
                            break;
                        case 2: // 多选题
                            result.put("multipleChoice", count);
                            break;
                        case 3: // 判断题
                            result.put("judgment", count);
                            break;
                    }
                }
            } catch (Exception e) {
                // 静默处理错误，不影响其他统计
                System.err.println("跳过无效统计数据: " + stat);
            }
        }

        result.put("total", total);
        return result;
    }

    /**
     * 根据题目ID列表查询题目
     *
     * @param questionIds 题目ID列表
     * @return 题目列表
     */
    @Override
    public List<Question> selectQuestionsByIds(List<Long> questionIds) {
        if (questionIds == null || questionIds.isEmpty()) {
            return new ArrayList<>();
        }
        return questionMapper.selectQuestionsByIds(questionIds);
    }

    /**
     * 导出题目为Word文档
     *
     * @param response HTTP响应
     * @param questions 题目列表
     * @param bankName 题库名称
     */
    @Override
    public void exportQuestionsToWord(HttpServletResponse response, List<Question> questions, String bankName) {
        try {
            // 创建Word文档
            XWPFDocument document = new XWPFDocument();

            // 设置标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText(bankName);
            titleRun.setBold(true);
            titleRun.setFontSize(16);
            titleRun.setFontFamily("宋体");

            // 添加空行
            document.createParagraph();

            // 遍历题目
            for (int i = 0; i < questions.size(); i++) {
                Question question = questions.get(i);
                QuestionDTO questionDTO = questionConverter.entityToDto(question);

                // 创建题目段落
                XWPFParagraph questionParagraph = document.createParagraph();
                XWPFRun questionRun = questionParagraph.createRun();

                // 题目序号和题型
                String questionTypeText = getQuestionTypeText(question.getQuestionType());
                String questionText = String.format("%d、[%s]%s",
                    i + 1, questionTypeText, cleanHtmlContent(question.getQuestionContent()));

                questionRun.setText(questionText);
                questionRun.setFontFamily("宋体");
                questionRun.setFontSize(12);

                // 如果是选择题，添加选项
                if (questionDTO.getOptions() != null && !questionDTO.getOptions().isEmpty()) {
                    for (QuestionDTO.OptionDTO option : questionDTO.getOptions()) {
                        XWPFParagraph optionParagraph = document.createParagraph();
                        XWPFRun optionRun = optionParagraph.createRun();
                        optionRun.setText(option.getKey() + "、" + cleanHtmlContent(option.getContent()));
                        optionRun.setFontFamily("宋体");
                        optionRun.setFontSize(12);
                    }
                }

                // 添加答案
                XWPFParagraph answerParagraph = document.createParagraph();
                XWPFRun answerRun = answerParagraph.createRun();
                String answerText = "答案：" + (questionDTO.getCorrectAnswer() != null ? questionDTO.getCorrectAnswer() : "");
                answerRun.setText(answerText);
                answerRun.setFontFamily("宋体");
                answerRun.setFontSize(12);

                // 添加空行分隔题目
                if (i < questions.size() - 1) {
                    document.createParagraph();
                }
            }

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setCharacterEncoding("utf-8");
            String fileName = java.net.URLEncoder.encode(bankName + ".docx", "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            // 写入响应
            document.write(response.getOutputStream());
            document.close();

        } catch (Exception e) {
            log.error("导出Word文档失败", e);
            throw new RuntimeException("导出Word文档失败: " + e.getMessage());
        }
    }

    /**
     * 获取题型文本
     */
    private String getQuestionTypeText(Integer questionType) {
        if (questionType == null) {
            return "未知题型";
        }
        switch (questionType) {
            case 1:
                return "单选题";
            case 2:
                return "多选题";
            case 3:
                return "判断题";
            default:
                return "未知题型";
        }
    }

    /**
     * 清理HTML内容
     */
    private String cleanHtmlContent(String content) {
        if (content == null) {
            return "";
        }
        // 简单的HTML标签清理
        return content.replaceAll("<[^>]*>", "").trim();
    }
}
